# Microservice Controller-to-Service Mapping

This document explains how nest-postman-sync maps controllers to specific microservice prefixes in a microservice architecture.

## Overview

In a microservice architecture, different controllers belong to different services, and each service may have its own API prefix. The challenge is determining which controllers belong to which service so that the correct prefix can be applied to their routes.

## Mapping Strategies

The system uses multiple strategies to map controllers to services, trying them in order until a match is found:

### 1. Explicit Service Mapping (Highest Priority)

Controllers can explicitly declare which service they belong to using metadata or decorators.

#### Using Custom Metadata
```typescript
// In your controller
@Controller('users')
@SetMetadata('service', 'auth')
export class UsersController {
  // This controller will use the 'auth' service prefix
}
```

#### Using ApiTags Decorator
```typescript
import { ApiTags } from '@nestjs/swagger';

@Controller('users')
@ApiTags('auth')  // If 'auth' is configured as a service name
export class UsersController {
  // This controller will use the 'auth' service prefix
}
```

### 2. Controller Path Analysis

The system analyzes the `@Controller()` path to determine service ownership.

#### Example Configuration
```json
{
  "nestjs": {
    "isMicroservice": true,
    "microservicePrefixes": {
      "auth": "/auth",
      "users": "/api/v1/users",
      "orders": "/orders"
    }
  }
}
```

#### Matching Examples
```typescript
// Will match 'auth' service because path starts with '/auth'
@Controller('/auth/login')
export class AuthController {}

// Will match 'users' service because path starts with '/api/v1/users'
@Controller('/api/v1/users/profile')
export class UserProfileController {}

// Will match 'orders' service because path contains 'orders'
@Controller('orders/management')
export class OrderManagementController {}
```

### 3. File Path Pattern Matching

The system analyzes the controller's file path to infer service ownership.

#### Common Project Structures

**Nx Monorepo Structure:**
```
apps/
├── auth-service/
│   └── src/controllers/auth.controller.ts     # → 'auth' service
├── user-service/
│   └── src/controllers/users.controller.ts    # → 'user' service
└── order-service/
    └── src/controllers/orders.controller.ts   # → 'order' service
```

**Modular Structure:**
```
src/
├── modules/
│   ├── auth/
│   │   └── auth.controller.ts                 # → 'auth' service
│   ├── users/
│   │   └── users.controller.ts                # → 'users' service
│   └── orders/
│       └── orders.controller.ts               # → 'orders' service
```

**Services Structure:**
```
src/
├── services/
│   ├── auth-service/
│   │   └── controllers/auth.controller.ts     # → 'auth' service
│   ├── user-service/
│   │   └── controllers/users.controller.ts    # → 'user' service
│   └── order-service/
│       └── controllers/orders.controller.ts   # → 'order' service
```

### 4. Controller Name Pattern Matching

The system analyzes the controller class name to infer service ownership.

#### Examples
```typescript
// Will match 'auth' service
export class AuthController {}
export class AuthenticationController {}
export class AuthUsersController {}

// Will match 'users' service  
export class UsersController {}
export class UserManagementController {}
export class UsersProfileController {}

// Will match 'orders' service
export class OrdersController {}
export class OrderManagementController {}
export class OrdersProcessingController {}
```

## Configuration Examples

### Basic Microservice Setup

```json
{
  "nestjs": {
    "isMicroservice": true,
    "microservicePrefixes": {
      "auth": "/auth",
      "users": "/users", 
      "orders": "/orders",
      "payments": "/payments"
    }
  }
}
```

### Advanced Microservice Setup

```json
{
  "nestjs": {
    "isMicroservice": true,
    "microservicePrefixes": {
      "authentication": "/api/v1/auth",
      "user-management": "/api/v1/users",
      "order-processing": "/api/v1/orders",
      "payment-gateway": "/api/v1/payments",
      "notification": "/api/v1/notifications"
    }
  }
}
```

## Debugging Controller Mappings

When you run `nest-postman-sync generate`, the system will analyze and log the controller-to-service mappings:

```bash
🔍 Analyzing controller-to-service mappings...
📋 Configured microservice prefixes:
   • auth: /auth
   • users: /users
   • orders: /orders

=== Controller-to-Service Mapping Analysis ===
Controller: AuthController
  File: /project/src/auth/auth.controller.ts
  Controller Path: login
  Mapped Service: auth
  Effective Prefix: /auth
---
Controller: UsersController  
  File: /project/src/users/users.controller.ts
  Controller Path: users
  Mapped Service: users
  Effective Prefix: /users
---

=== Mapping Summary ===
Total Controllers: 5
Mapped to Services: 4
Using Global Prefix: 1
```

## Troubleshooting

### Controllers Not Mapping to Expected Services

1. **Check Service Names**: Ensure service names in configuration match your project structure
2. **Verify File Paths**: Check if controller file paths contain service names
3. **Review Controller Paths**: Ensure `@Controller()` paths align with service prefixes
4. **Use Explicit Mapping**: Add explicit service metadata to controllers

### Multiple Services Matching

If multiple services could match a controller, the system uses the first match found in this order:
1. Explicit metadata
2. Controller path analysis  
3. File path patterns
4. Controller name patterns

### No Services Matching

Controllers that don't match any service will use the global prefix (if configured) or no prefix.

## Best Practices

1. **Use Consistent Naming**: Keep service names, file paths, and controller paths consistent
2. **Explicit is Better**: Use explicit service metadata for critical controllers
3. **Test Mappings**: Run generation with debug logging to verify mappings
4. **Document Structure**: Document your project's service organization
5. **Regular Validation**: Periodically review mapping results as your project evolves
