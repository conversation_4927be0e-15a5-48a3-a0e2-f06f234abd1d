# nest-postman-sync

A production-grade CLI tool for syncing NestJS projects with Postman collections.

## 🚀 Features

- **Automatic Generation**: Parse NestJS controllers and generate Postman collections
- **Live Sync**: Watch for file changes and automatically sync to Postman
- **State Management**: Track changes and sync only when necessary
- **Rich Configuration**: Flexible configuration with environment variable support
- **Robust Logging**: Comprehensive logging with multiple levels and output options
- **Type Safety**: Built with TypeScript for maximum reliability

## 📦 Installation

```bash
npm install -g nest-postman-sync
```

Or use locally in your project:

```bash
npm install --save-dev nest-postman-sync
```

## 🛠️ Quick Start

1. **Initialize configuration**:
   ```bash
   nest-postman-sync init
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your Postman API key
   ```

3. **Generate collection**:
   ```bash
   nest-postman-sync generate
   ```

4. **Sync to <PERSON><PERSON>**:
   ```bash
   nest-postman-sync sync
   ```

5. **Watch for changes**:
   ```bash
   nest-postman-sync watch
   ```

## ⚙️ Configuration

### Config File (`postman.config.json`)

```json
{
  "postman": {
    "apiKey": "${POSTMAN_API_KEY}",
    "collectionId": "${POSTMAN_COLLECTION_ID}",
    "workspaceId": "${POSTMAN_WORKSPACE_ID}",
    "baseUrl": "https://api.postman.com"
  },
  "nestjs": {
    "projectRoot": ".",
    "srcDir": "src",
    "controllersPattern": ["**/*.controller.ts"],
    "excludePatterns": ["**/*.spec.ts", "**/*.test.ts"]
  },
  "sync": {
    "outputFile": "postman-collection.json",
    "watchMode": false,
    "watchPaths": ["src/**/*.ts"],
    "debounceMs": 1000
  },
  "options": {
    "generateDocs": true,
    "includePrivate": false,
    "includeDeprecated": false,
    "overwriteExisting": true
  }
}
```

### Environment Variables

```bash
POSTMAN_API_KEY=your_postman_api_key
POSTMAN_COLLECTION_ID=optional_collection_id
POSTMAN_WORKSPACE_ID=optional_workspace_id
NESTJS_PROJECT_ROOT=.
NESTJS_SRC_DIR=src
```

## 🔧 Commands

### `nest-postman-sync init`

Initialize project configuration. Creates `postman.config.json` and `.env.example`.

**Options:**
- `--force` - Overwrite existing configuration files

### `nest-postman-sync generate`

Generate Postman collection from NestJS controllers.

**Options:**
- `--output <file>` - Output file path (default: postman-collection.json)
- `--format` - Format the output JSON

### `nest-postman-sync sync`

Sync generated collection to Postman workspace.

**Options:**
- `--collection-id <id>` - Target collection ID (creates new if not provided)
- `--workspace-id <id>` - Target workspace ID

### `nest-postman-sync watch`

Watch for file changes and automatically sync.

**Options:**
- `--debounce <ms>` - Debounce delay in milliseconds (default: 1000)
- `--paths <patterns>` - File patterns to watch

## 🎛️ Global Options

- `-v, --verbose` - Enable verbose logging
- `-q, --quiet` - Enable quiet mode (warnings and errors only)
- `--debug` - Enable debug mode with stack traces
- `--log-file <path>` - Write logs to file
- `-c, --config <path>` - Custom configuration file path

## 🧪 Examples

```bash
# Generate with verbose output
nest-postman-sync generate --verbose

# Sync to specific collection
nest-postman-sync sync --collection-id abc123

# Watch with custom debounce
nest-postman-sync watch --debounce 2000

# Debug mode with file logging
nest-postman-sync generate --debug --log-file debug.log
```

## 🏗️ Development

### Setup

```bash
git clone https://github.com/your-org/nest-postman-sync.git
cd nest-postman-sync
npm install
```

### Build

```bash
npm run build
```

### Test

```bash
npm test          # Run tests in watch mode
npm run test:ci   # Run tests with coverage
```

### Lint & Format

```bash
npm run lint      # Check for linting errors
npm run lint:fix  # Fix linting errors
npm run format    # Format code with Prettier
```

### Local Development

```bash
npm run dev       # Run in development mode
npm link          # Link for global usage
```

## 📋 Requirements

- Node.js >= 16.0.0
- NestJS project with TypeScript
- Postman API key

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and development process.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙋‍♂️ Support

- [Documentation](docs/)
- [Issues](https://github.com/your-org/nest-postman-sync/issues)
- [Discussions](https://github.com/your-org/nest-postman-sync/discussions)

## 🚧 Roadmap

- [ ] OpenAPI/Swagger integration
- [ ] Multiple collection support
- [ ] Custom request templates
- [ ] Environment variable management
- [ ] Mock response generation

---

Made with ❤️ for the NestJS community
