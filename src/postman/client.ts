import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { logger } from '@/utils/logger';
import { PostmanError } from './errors';

/**
 * Postman API types - following official API documentation
 */
export interface Workspace {
  id: string;
  name: string;
  type: string;
  visibility: string;
  createdBy: string;
}

export interface Collection {
  id: string;
  name: string;
  owner: string;
  createdAt: string;
  updatedAt: string;
  uid: string;
  isPublic: boolean;
}

/**
 * Postman API Request interfaces
 */
export interface CreateWorkspaceRequest {
  workspace: {
    name: string;
    type: string;
    description: string;
  };
}

// Auth types
export type AuthType = 'apikey' | 'bearer' | 'basic' | 'oauth2' | 'noauth';

export interface AuthApiKey {
  type: 'apikey';
  apikey: Array<{
    key: 'value' | 'key' | 'in';
    value: string;
  }>;
}

export interface AuthBearer {
  type: 'bearer';
  bearer: Array<{
    key: 'token';
    value: string;
  }>;
}

export type PostmanAuth = AuthApiKey | AuthBearer | { type: 'noauth' } | Record<string, any>;

// Request body
export interface PostmanRequestBody {
  mode: 'raw' | 'urlencoded' | 'formdata' | 'file' | 'graphql';
  raw?: string;
  options?: {
    raw?: { language: string };
  };
  // ...other modes can be added as needed
}

// Request URL
export interface PostmanUrl {
  raw: string;
  protocol: string;
  host: string[];
  path: string[];
  query?: Array<{ key: string; value: string; description?: string; disabled?: boolean }>;
  variable?: Array<{ key: string; value: string; description?: string }>;
}

// Request
export interface PostmanRequest {
  method: string;
  header: Array<{ key: string; value: string; type?: string; description?: string }>;
  body?: PostmanRequestBody;
  url: PostmanUrl;
  description?: string;
}

// Response
export interface PostmanResponse {
  name: string;
  originalRequest: PostmanRequest;
  status: string;
  code: number;
  _postman_previewlanguage?: string;
  header: Array<{ key: string; value: string; description?: string; type?: string; enabled?: boolean }>;
  cookie?: any[];
  responseTime?: number | null;
  body: string;
}

// Item (can be a folder or a request)
export interface PostmanItem {
  name: string;
  item?: PostmanItem[]; // folder
  event?: PostmanEvent[];
  protocolProfileBehavior?: Record<string, any>;
  request?: PostmanRequest;
  response?: PostmanResponse[];
  description?: string;
}

// Event (prerequest/test scripts)
export interface PostmanEvent {
  listen: 'prerequest' | 'test';
  script: {
    type: 'text/javascript';
    exec: string[];
    packages?: Record<string, any>;
  };
}

// Info
export interface PostmanInfo {
  name: string;
  description?: string | undefined;
  schema: string;
}

// The full collection
export interface StrictCreateCollectionRequest {
  collection: {
    info: PostmanInfo;
    auth?: PostmanAuth;
    item?: PostmanItem[];
    event?: PostmanEvent[];
    variable?: Array<{ key: string; value: string; type?: string; description?: string }>;
  };
}

/**
 * Postman API Response interfaces
 */
export interface ListWorkspacesResponse {
  workspaces: Workspace[];
}

export interface CreateWorkspaceResponse {
  workspace: {
    id: string;
    name: string;
  };
}

export interface ListCollectionsResponse {
  collections: Collection[];
}

export interface CreateCollectionResponse {
  collection: {
    id: string;
    name: string;
    uid: string;
  };
}

// For updateCollection
export interface CollectionCreateBody {
  name: string;
  description?: string;
  schema?: string;
  item?: PostmanItem[];
  variable?: Array<{ key: string; value: string; type?: string; description?: string }>;
  auth?: PostmanAuth;
  event?: PostmanEvent[];
}

export interface CollectionResponse {
  collection?: Collection;
  error?: { message: string };
}

/**
 * Postman API HTTP Client
 */
class PostmanClient {
  private client: AxiosInstance;
  private readonly baseURL = 'https://api.postman.com';

  constructor() {
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30 second timeout
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'nest-postman-sync/1.0.0',
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for logging and error handling
   */
  private setupInterceptors(): void {
    // Request interceptor - attach API key and log requests
    this.client.interceptors.request.use(
      (config) => {
        // Attach API key from environment
        const apiKey = process.env['POSTMAN_API_KEY'];
        if (!apiKey) {
          throw new Error('POSTMAN_API_KEY environment variable is required');
        }
        
        config.headers['X-API-Key'] = apiKey;

        // Log outgoing request
        const method = config.method?.toUpperCase() || 'GET';
        const url = config.url || '';
        logger.debug(`[postman] → ${method} ${url}`, { module: 'postman' });

        return config;
      },
      (error) => {
        logger.error('[postman][error] Request setup failed', error, { module: 'postman' });
        return Promise.reject(error);
      }
    );

    // Response interceptor - log responses and handle errors
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // Log successful response
        const method = response.config.method?.toUpperCase() || 'GET';
        const url = response.config.url || '';
        const status = response.status;
        logger.info(`[postman] ← ${method} ${url} · ${status}`, { module: 'postman' });

        return response;
      },
      (error: AxiosError) => {
        // Extract error details
        const method = error.config?.method?.toUpperCase() || 'UNKNOWN';
        const url = error.config?.url || 'unknown';
        const status = error.response?.status;
        const responseBody = error.response?.data;

        // Log error
        const statusText = status ? ` · ${status}` : '';
        logger.error(`[postman][error] ${method} ${url}${statusText} – ${error.message}`, error, { 
          module: 'postman'
        });

        // Create custom PostmanError
        const errorMeta: { method: string; url: string; status?: number; body?: any } = {
          method,
          url,
          body: responseBody,
        };
        
        if (status !== undefined) {
          errorMeta.status = status;
        }
        
        const postmanError = new PostmanError(
          errorMeta,
          this.extractErrorMessage(error, responseBody)
        );

        return Promise.reject(postmanError);
      }
    );
  }

  /**
   * Extract meaningful error message from API response
   */
  private extractErrorMessage(error: AxiosError, responseBody?: any): string {
    // Check if Postman API returned an error message
    if (responseBody?.error?.message) {
      return responseBody.error.message;
    }

    // Check for common HTTP status codes
    if (error.response?.status) {
      switch (error.response.status) {
        case 401:
          return 'Invalid API key or unauthorized access';
        case 403:
          return 'Forbidden - insufficient permissions';
        case 404:
          return 'Resource not found';
        case 429:
          return 'Rate limit exceeded - please try again later';
        case 500:
          return 'Postman API server error';
        case 503:
          return 'Postman API service unavailable';
        default:
          return error.message || 'Unknown API error';
      }
    }

    // Network or timeout errors
    if (error.code === 'ENOTFOUND') {
      return 'Network error - cannot reach Postman API';
    }
    if (error.code === 'ETIMEDOUT') {
      return 'Request timeout - Postman API did not respond in time';
    }

    return error.message || 'Unknown error occurred';
  }

  /**
   * Generic API request method with retry support
   */
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    retries = 3
  ): Promise<T> {
    let lastError: PostmanError;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const config: AxiosRequestConfig = {
          method,
          url: endpoint,
          ...(data && { data }),
        };

        const response = await this.client.request<T>(config);
        return response.data;
      } catch (error) {
        lastError = error as PostmanError;

        // Don't retry on client errors (4xx) except rate limiting
        if (lastError.meta.status && lastError.meta.status >= 400 && lastError.meta.status < 500) {
          if (lastError.meta.status !== 429) {
            throw lastError;
          }
        }

        // Don't retry on last attempt
        if (attempt === retries) {
          throw lastError;
        }

        // Exponential backoff: 1s, 2s, 4s
        const delay = Math.pow(2, attempt - 1) * 1000;
        logger.debug(`[postman] Retrying in ${delay}ms (attempt ${attempt}/${retries})`, { module: 'postman' });
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * List all workspaces
   */
  async listWorkspaces(): Promise<Workspace[]> {
    try {
      const response = await this.request<ListWorkspacesResponse>('GET', '/workspaces');
      return response.workspaces || [];
    } catch (error) {
      if (error instanceof PostmanError) {
        throw error;
      }
      throw new PostmanError(
        { method: 'GET', url: '/workspaces' },
        'Failed to list workspaces'
      );
    }
  }

  /**
   * Create a new workspace
   */
  async createWorkspace(name: string, type: string, description?: string): Promise<Workspace> {
    try {
      const endpoint = '/workspaces';
      const payload: CreateWorkspaceRequest = {
        workspace: {
          name,
          type,
          description: description || `Workspace for ${name}`,
        },
      };

      const response = await this.request<CreateWorkspaceResponse>('POST', endpoint, payload);
      
      if (!response.workspace) {
        throw new PostmanError(
          { method: 'POST', url: endpoint },
          'Invalid response: missing workspace data'
        );
      }

      // Convert the response to full workspace format
      return {
        id: response.workspace.id,
        name: response.workspace.name,
        type,
        visibility: 'personal',
        createdBy: 'current-user'
      };
    } catch (error) {
      if (error instanceof PostmanError) {
        throw error;
      }
      throw new PostmanError(
        { method: 'POST', url: '/workspaces' },
        'Failed to create workspace'
      );
    }
  }

  /**
   * List collections in a workspace
   */
  async listCollections(workspaceId: string): Promise<Collection[]> {
    try {
      const endpoint = `/collections?workspace=${workspaceId}`;
      const response = await this.request<ListCollectionsResponse>('GET', endpoint);
      return response.collections || [];
    } catch (error) {
      if (error instanceof PostmanError) {
        throw error;
      }
      throw new PostmanError(
        { method: 'GET', url: `/collections?workspace=${workspaceId}` },
        'Failed to list collections'
      );
    }
  }

  /**
   * Create a new collection
   */
  async createCollection(workspaceId: string, name: string, description?: string): Promise<Collection> {
    try {
      const endpoint = `/collections?workspace=${workspaceId}`;
      const payload: StrictCreateCollectionRequest = {
        collection: {
          info: {
            name,
            description,
            schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
          },
          item: [],
        },
      };

      const response = await this.request<CreateCollectionResponse>('POST', endpoint, payload);
      
      if (!response.collection) {
        throw new PostmanError(
          { method: 'POST', url: endpoint },
          'Invalid response: missing collection data'
        );
      }

      // Convert the response to full collection format
      return {
        id: response.collection.id,
        name: response.collection.name,
        uid: response.collection.uid,
        owner: 'current-user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isPublic: false
      };
    } catch (error) {
      if (error instanceof PostmanError) {
        throw error;
      }
      throw new PostmanError(
        { method: 'POST', url: `/collections?workspace=${workspaceId}` },
        'Failed to create collection'
      );
    }
  }

  /**
   * Update an existing collection
   */
  async updateCollection(collectionUid: string, body: CollectionCreateBody): Promise<Collection> {
    try {
      const endpoint = `/collections/${collectionUid}`;
      const payload = {
        collection: {
          info: {
            name: body.name,
            description: body.description,
            schema: body.schema || 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
          },
          item: body.item || [],
          variable: body.variable || [],
          auth: body.auth,
          event: body.event || [],
        },
      };

      const response = await this.request<CollectionResponse>('PUT', endpoint, payload);
      
      if (response.error) {
        throw new PostmanError(
          { method: 'PUT', url: endpoint },
          response.error.message
        );
      }

      if (!response.collection) {
        throw new PostmanError(
          { method: 'PUT', url: endpoint },
          'Invalid response: missing collection data'
        );
      }

      return response.collection;
    } catch (error) {
      if (error instanceof PostmanError) {
        throw error;
      }
      throw new PostmanError(
        { method: 'PUT', url: `/collections/${collectionUid}` },
        'Failed to update collection'
      );
    }
  }
}

// Export singleton instance
export const postmanClient = new PostmanClient();

// Export types and classes
export { PostmanClient };
