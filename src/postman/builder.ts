import { logger } from '@/utils/logger';
import { ParsedController } from '@/parser/ast-parser';

export interface PostmanCollection {
  info: {
    name: string;
    description: string;
    version: string;
    schema: string;
  };
  item: PostmanItem[];
  variable?: PostmanVariable[];
  auth?: PostmanAuth;
}

export interface PostmanItem {
  name: string;
  request: PostmanRequest;
  response?: PostmanResponse[];
  description?: string;
}

export interface PostmanRequest {
  method: string;
  header: PostmanHeader[];
  url: PostmanUrl;
  body?: PostmanBody;
  description?: string;
}

export interface PostmanUrl {
  raw: string;
  host: string[];
  path: string[];
  query?: PostmanQuery[];
}

export interface PostmanHeader {
  key: string;
  value: string;
  description?: string;
  disabled?: boolean;
}

export interface PostmanQuery {
  key: string;
  value: string;
  description?: string;
  disabled?: boolean;
}

export interface PostmanBody {
  mode: 'raw' | 'formdata' | 'urlencoded';
  raw?: string;
  options?: {
    raw?: {
      language: string;
    };
  };
}

export interface PostmanResponse {
  name: string;
  originalRequest: PostmanRequest;
  status: string;
  code: number;
  header: PostmanHeader[];
  body: string;
}

export interface PostmanVariable {
  key: string;
  value: string;
  type: string;
  description?: string;
}

export interface PostmanAuth {
  type: string;
  bearer?: {
    token: string;
  };
  apikey?: {
    key: string;
    value: string;
    in: string;
  };
}

export class PostmanCollectionBuilder {
  private collection: PostmanCollection;

  constructor(name: string, description = '') {
    this.collection = {
      info: {
        name,
        description,
        version: '1.0.0',
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      },
      item: [],
    };
  }

  /**
   * Build Postman collection from parsed controllers
   */
  buildFromControllers(controllers: ParsedController[]): PostmanCollection {
    logger.debug(`Building collection from ${controllers.length} controllers`, { module: 'builder' });

    // Placeholder for collection building logic
    logger.warn('Collection building not yet implemented', { module: 'builder' });

    return this.collection;
  }

  /**
   * Add a controller as a folder in the collection
   */
  addController(controller: ParsedController): void {
    logger.debug(`Adding controller: ${controller.name}`, { module: 'builder' });

    // Placeholder for controller addition logic
    logger.warn('Controller addition not yet implemented', { module: 'builder' });
  }

  // Note: Placeholder method commented out to avoid TypeScript unused variable errors
  // Will be implemented in future versions
  
  // private createRequestItem(method: any, basePath: string): PostmanItem {
  //   return {
  //     name: 'Placeholder Request',
  //     request: {
  //       method: 'GET',
  //       header: [],
  //       url: {
  //         raw: 'http://localhost:3000',
  //         host: ['localhost:3000'],
  //         path: [],
  //       },
  //     },
  //   };
  // }

  /**
   * Set collection variables
   */
  setVariables(variables: Record<string, string>): void {
    this.collection.variable = Object.entries(variables).map(([key, value]) => ({
      key,
      value,
      type: 'string',
    }));
  }

  /**
   * Set collection authentication
   */
  setAuth(auth: PostmanAuth): void {
    this.collection.auth = auth;
  }

  /**
   * Get the built collection
   */
  getCollection(): PostmanCollection {
    return this.collection;
  }

  /**
   * Export collection as JSON string
   */
  toJSON(): string {
    return JSON.stringify(this.collection, null, 2);
  }
}
