import { logger } from '@/utils/logger';
import { ParsedController } from '@/parser/ast-parser';

export interface PostmanCollection {
  info: {
    name: string;
    description: string;
    version: string;
    schema: string;
  };
  item: PostmanItem[];
  variable?: PostmanVariable[];
  auth?: PostmanAuth;
}

export interface PostmanItem {
  name: string;
  request?: PostmanRequest;
  item?: PostmanItem[];
  response?: PostmanResponse[];
  description?: string;
}

export interface PostmanRequest {
  method: string;
  header: PostmanHeader[];
  url: PostmanUrl;
  body?: PostmanBody | undefined;
  description?: string;
}

export interface PostmanUrl {
  raw: string;
  host: string[];
  path: string[];
  query?: PostmanQuery[] | undefined;
}

export interface PostmanHeader {
  key: string;
  value: string;
  description?: string;
  disabled?: boolean;
}

export interface PostmanQuery {
  key: string;
  value: string;
  description?: string;
  disabled?: boolean;
}

export interface PostmanBody {
  mode: 'raw' | 'formdata' | 'urlencoded';
  raw?: string;
  options?: {
    raw?: {
      language: string;
    };
  };
}

export interface PostmanResponse {
  name: string;
  originalRequest: PostmanRequest;
  status: string;
  code: number;
  header: PostmanHeader[];
  body: string;
}

export interface PostmanVariable {
  key: string;
  value: string;
  type: string;
  description?: string;
}

export interface PostmanAuth {
  type: string;
  bearer?: Array<{
    key: 'token';
    value: string;
  }>;
  apikey?: Array<{
    key: 'value' | 'key' | 'in';
    value: string;
  }>;
}

export class PostmanCollectionBuilder {
  private collection: PostmanCollection;
  private globalPrefix: string = '';
  private microservicePrefixes: Record<string, string> = {};
  private isMicroservice: boolean = false;

  constructor(name: string, description = '') {
    this.collection = {
      info: {
        name,
        description,
        version: '1.0.0',
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
      },
      item: [],
    };
  }

  /**
   * Build Postman collection from parsed controllers
   */
  buildFromControllers(controllers: ParsedController[]): PostmanCollection {
    logger.debug(`Building collection from ${controllers.length} controllers`, { module: 'builder' });

    // Clear existing items
    this.collection.item = [];

    // Add each controller as a folder
    for (const controller of controllers) {
      this.addController(controller);
    }

    logger.debug(`Built collection with ${this.collection.item.length} folders`, { module: 'builder' });
    return this.collection;
  }

  /**
   * Add a controller as a folder in the collection
   */
  addController(controller: ParsedController): void {
    logger.debug(`Adding controller: ${controller.name}`, { module: 'builder' });

    const folderItem: PostmanItem = {
      name: controller.name,
      item: [],
      description: this.buildControllerDescription(controller),
    };

    // Add each method as a request
    for (const method of controller.methods) {
      const requestItem = this.createRequestItem(method, controller);
      if (requestItem) {
        (folderItem.item as PostmanItem[]).push(requestItem);
      }
    }

    this.collection.item.push(folderItem);
  }

  /**
   * Build controller description
   */
  private buildControllerDescription(controller: ParsedController): string {
    let description = `Controller: ${controller.name}\n`;
    description += `Path: ${controller.path}\n`;

    if (controller.guards.length > 0) {
      description += `Guards: ${controller.guards.join(', ')}\n`;
    }

    if (controller.metadata['description']) {
      description += `\n${controller.metadata['description']}`;
    }

    return description;
  }

  /**
   * Create a request item from a parsed method
   */
  private createRequestItem(method: any, controller: ParsedController): PostmanItem | null {
    // Build URL with appropriate prefix (global or microservice-specific)
    const url = this.buildUrl(method.path, method.parameters, controller.path, controller);
    const headers = this.buildHeaders(method);
    const body = this.buildRequestBody(method.parameters);

    const request: PostmanRequest = {
      method: method.httpMethod,
      header: headers,
      url,
      body,
      description: method.description || `${method.httpMethod} ${url.raw}`,
    };

    // Add authentication if guards are present
    if (method.guards.length > 0 || controller.guards.length > 0) {
      // Auth will be handled at collection level
    }

    return {
      name: `${method.httpMethod} ${method.name}`,
      request,
      description: method.description,
    };
  }



  /**
   * Build Postman URL object
   */
  private buildUrl(path: string, parameters: any[], controllerPath?: string, controller?: ParsedController): PostmanUrl {
    const baseUrl = '{{baseUrl}}';

    // Build the complete path: servicePrefix + controllerPath + methodPath
    let fullPath = '';

    // Determine the appropriate prefix
    let effectivePrefix = this.globalPrefix;

    if (this.isMicroservice && controller) {
      // Try to find microservice-specific prefix
      const serviceName = this.extractServiceNameFromController(controller);
      if (serviceName && this.microservicePrefixes[serviceName]) {
        effectivePrefix = this.microservicePrefixes[serviceName];
      }
    }

    // Add prefix if exists
    if (effectivePrefix && effectivePrefix !== '') {
      const normalizedPrefix = effectivePrefix.startsWith('/')
        ? effectivePrefix
        : `/${effectivePrefix}`;
      const cleanPrefix = normalizedPrefix.endsWith('/')
        ? normalizedPrefix.slice(0, -1)
        : normalizedPrefix;
      fullPath += cleanPrefix;
    }

    // Add controller prefix if exists
    if (controllerPath && controllerPath !== '') {
      const normalizedControllerPath = controllerPath.startsWith('/')
        ? controllerPath
        : `/${controllerPath}`;
      const cleanControllerPath = normalizedControllerPath.endsWith('/')
        ? normalizedControllerPath.slice(0, -1)
        : normalizedControllerPath;
      fullPath += cleanControllerPath;
    }

    // Add method path
    if (path && path !== '') {
      const normalizedPath = path.startsWith('/') ? path : `/${path}`;
      fullPath += normalizedPath;
    }

    // Ensure we have at least a root path
    if (!fullPath) {
      fullPath = '/';
    }

    const pathSegments = fullPath.split('/').filter(segment => segment.length > 0);

    // Replace path parameters with Postman variables
    const processedSegments = pathSegments.map(segment => {
      if (segment.startsWith(':')) {
        return `{{${segment.slice(1)}}}`;
      }
      return segment;
    });

    // Extract query parameters
    const queryParams: PostmanQuery[] = [];
    for (const param of parameters) {
      if (param.location === 'query') {
        queryParams.push({
          key: param.name,
          value: param.example || `{{${param.name}}}`,
          description: param.description,
          disabled: !param.required,
        });
      }
    }

    const finalPath = processedSegments.length > 0 ? `/${processedSegments.join('/')}` : '/';

    return {
      raw: `${baseUrl}${finalPath}`,
      host: [baseUrl],
      path: processedSegments,
      query: queryParams.length > 0 ? queryParams : undefined,
    };
  }

  /**
   * Build headers for request
   */
  private buildHeaders(method: any): PostmanHeader[] {
    const headers: PostmanHeader[] = [];

    // Add content-type for requests with body
    const hasBody = method.parameters.some((p: any) => p.location === 'body');
    if (hasBody) {
      headers.push({
        key: 'Content-Type',
        value: 'application/json',
        description: 'Request content type',
      });
    }

    // Add custom headers from parameters
    for (const param of method.parameters) {
      if (param.location === 'header') {
        headers.push({
          key: param.name,
          value: param.example || `{{${param.name}}}`,
          description: param.description,
          disabled: !param.required,
        });
      }
    }

    return headers;
  }

  /**
   * Build request body
   */
  private buildRequestBody(parameters: any[]): PostmanBody | undefined {
    const bodyParam = parameters.find(p => p.location === 'body');

    if (!bodyParam) {
      return undefined;
    }

    const bodyContent = bodyParam.schema || bodyParam.example || {};

    return {
      mode: 'raw',
      raw: JSON.stringify(bodyContent, null, 2),
      options: {
        raw: {
          language: 'json',
        },
      },
    };
  }

  /**
   * Set collection variables
   */
  setVariables(variables: Record<string, string>): void {
    this.collection.variable = Object.entries(variables).map(([key, value]) => ({
      key,
      value,
      type: 'string',
    }));
  }

  /**
   * Set global API prefix
   */
  setGlobalPrefix(prefix: string): void {
    this.globalPrefix = prefix;
    logger.debug(`Set global prefix: ${prefix}`, { module: 'builder' });
  }

  /**
   * Set microservice configuration
   */
  setMicroserviceConfig(isMicroservice: boolean, prefixes?: Record<string, string>): void {
    this.isMicroservice = isMicroservice;
    this.microservicePrefixes = prefixes || {};
    logger.debug(`Set microservice config: ${isMicroservice}, prefixes: ${JSON.stringify(prefixes)}`, { module: 'builder' });
  }

  /**
   * Debug method to analyze controller-to-service mappings
   * Useful for troubleshooting microservice prefix assignments
   */
  analyzeControllerServiceMappings(controllers: ParsedController[]): void {
    if (!this.isMicroservice || Object.keys(this.microservicePrefixes).length === 0) {
      logger.info('Not a microservice setup or no prefixes configured', { module: 'builder' });
      return;
    }

    logger.info('=== Controller-to-Service Mapping Analysis ===', { module: 'builder' });
    logger.info(`Configured services: ${Object.keys(this.microservicePrefixes).join(', ')}`, { module: 'builder' });

    const mappingResults: Array<{
      controller: string;
      filePath: string;
      controllerPath: string;
      mappedService: string | null;
      effectivePrefix: string;
    }> = [];

    for (const controller of controllers) {
      const serviceName = this.extractServiceNameFromController(controller);
      const effectivePrefix = serviceName && this.microservicePrefixes[serviceName]
        ? this.microservicePrefixes[serviceName]
        : this.globalPrefix;

      mappingResults.push({
        controller: controller.name,
        filePath: controller.filePath,
        controllerPath: controller.path,
        mappedService: serviceName,
        effectivePrefix: effectivePrefix
      });
    }

    // Log results
    mappingResults.forEach(result => {
      logger.info(`Controller: ${result.controller}`, { module: 'builder' });
      logger.info(`  File: ${result.filePath}`, { module: 'builder' });
      logger.info(`  Controller Path: ${result.controllerPath}`, { module: 'builder' });
      logger.info(`  Mapped Service: ${result.mappedService || 'None (using global prefix)'}`, { module: 'builder' });
      logger.info(`  Effective Prefix: ${result.effectivePrefix || 'None'}`, { module: 'builder' });
      logger.info('---', { module: 'builder' });
    });

    // Summary
    const mappedCount = mappingResults.filter(r => r.mappedService).length;
    const unmappedCount = mappingResults.length - mappedCount;

    logger.info(`=== Mapping Summary ===`, { module: 'builder' });
    logger.info(`Total Controllers: ${mappingResults.length}`, { module: 'builder' });
    logger.info(`Mapped to Services: ${mappedCount}`, { module: 'builder' });
    logger.info(`Using Global Prefix: ${unmappedCount}`, { module: 'builder' });

    if (unmappedCount > 0) {
      logger.warn(`${unmappedCount} controllers could not be mapped to specific services`, { module: 'builder' });
      logger.info('Consider updating your microservice prefix configuration or controller organization', { module: 'builder' });

      // Provide suggestions for unmapped controllers
      const unmappedControllers = mappingResults.filter(r => !r.mappedService);
      this.suggestServiceMappings(unmappedControllers);
    }
  }

  /**
   * Suggest potential service mappings for unmapped controllers
   */
  private suggestServiceMappings(unmappedControllers: Array<{
    controller: string;
    filePath: string;
    controllerPath: string;
    mappedService: string | null;
    effectivePrefix: string;
  }>): void {
    logger.info('=== Suggestions for Unmapped Controllers ===', { module: 'builder' });

    for (const controller of unmappedControllers) {
      logger.info(`Controller: ${controller.controller}`, { module: 'builder' });

      const suggestions = this.generateMappingSuggestions(controller);
      if (suggestions.length > 0) {
        logger.info('  Suggested service mappings:', { module: 'builder' });
        suggestions.forEach(suggestion => {
          logger.info(`    • Add "${suggestion.serviceName}": "${suggestion.prefix}" to microservicePrefixes`, { module: 'builder' });
          logger.info(`      Reason: ${suggestion.reason}`, { module: 'builder' });
        });
      } else {
        logger.info('  No automatic suggestions available. Consider:', { module: 'builder' });
        logger.info('    • Adding explicit @SetMetadata("service", "serviceName") to the controller', { module: 'builder' });
        logger.info('    • Organizing the controller file path to include service name', { module: 'builder' });
        logger.info('    • Using @ApiTags("serviceName") if the service is configured', { module: 'builder' });
      }
      logger.info('---', { module: 'builder' });
    }
  }

  /**
   * Generate mapping suggestions for a controller
   */
  private generateMappingSuggestions(controller: {
    controller: string;
    filePath: string;
    controllerPath: string;
  }): Array<{ serviceName: string; prefix: string; reason: string }> {
    const suggestions: Array<{ serviceName: string; prefix: string; reason: string }> = [];

    // Extract potential service names from file path
    const pathParts = controller.filePath.toLowerCase().split(/[/\\]/);
    const potentialServices = pathParts.filter(part =>
      part.length > 2 &&
      !['src', 'app', 'apps', 'lib', 'libs', 'modules', 'services', 'controllers', 'controller'].includes(part) &&
      !part.includes('.') &&
      !part.includes('node_modules')
    );

    // Extract from controller path
    if (controller.controllerPath) {
      const controllerPathParts = controller.controllerPath.split('/').filter(p => p.length > 0);
      potentialServices.push(...controllerPathParts);
    }

    // Extract from controller name
    const controllerName = controller.controller.toLowerCase().replace('controller', '');
    if (controllerName.length > 0) {
      potentialServices.push(controllerName);
    }

    // Generate suggestions from potential services
    const uniqueServices = [...new Set(potentialServices)];
    for (const serviceName of uniqueServices) {
      if (serviceName.length > 1) {
        suggestions.push({
          serviceName: serviceName,
          prefix: `/${serviceName}`,
          reason: `Inferred from ${this.getInferenceSource(serviceName, controller)}`
        });
      }
    }

    return suggestions.slice(0, 3); // Limit to top 3 suggestions
  }

  /**
   * Determine the source of service name inference
   */
  private getInferenceSource(serviceName: string, controller: {
    controller: string;
    filePath: string;
    controllerPath: string;
  }): string {
    if (controller.filePath.toLowerCase().includes(serviceName)) {
      return 'file path';
    }
    if (controller.controllerPath.toLowerCase().includes(serviceName)) {
      return 'controller path';
    }
    if (controller.controller.toLowerCase().includes(serviceName)) {
      return 'controller name';
    }
    return 'analysis';
  }

  /**
   * Extract service name from controller for microservice prefix mapping
   * Uses multiple strategies to match controllers to services
   */
  private extractServiceNameFromController(controller: ParsedController): string | null {
    const filePath = controller.filePath || '';

    if (!filePath || Object.keys(this.microservicePrefixes).length === 0) {
      return null;
    }

    // Strategy 1: Check for explicit service mapping in controller metadata
    const explicitService = this.extractExplicitServiceMapping(controller);
    if (explicitService) {
      logger.debug(`Found explicit service mapping: ${controller.name} -> ${explicitService}`, { module: 'builder' });
      return explicitService;
    }

    // Strategy 2: Match by controller path prefix
    const pathPrefixService = this.extractServiceFromControllerPath(controller);
    if (pathPrefixService) {
      logger.debug(`Found service by controller path: ${controller.name} -> ${pathPrefixService}`, { module: 'builder' });
      return pathPrefixService;
    }

    // Strategy 3: Match by file path patterns
    const filePathService = this.extractServiceFromFilePath(controller);
    if (filePathService) {
      logger.debug(`Found service by file path: ${controller.name} -> ${filePathService}`, { module: 'builder' });
      return filePathService;
    }

    // Strategy 4: Match by controller name patterns
    const controllerNameService = this.extractServiceFromControllerName(controller);
    if (controllerNameService) {
      logger.debug(`Found service by controller name: ${controller.name} -> ${controllerNameService}`, { module: 'builder' });
      return controllerNameService;
    }

    logger.debug(`No service mapping found for controller: ${controller.name} (${filePath})`, { module: 'builder' });
    return null;
  }

  /**
   * Strategy 1: Extract service from explicit metadata or decorators
   * Looks for custom decorators or metadata that explicitly specify the service
   */
  private extractExplicitServiceMapping(controller: ParsedController): string | null {
    // Check if controller metadata contains service information
    if (controller.metadata['service']) {
      const serviceName = controller.metadata['service'] as string;
      if (this.microservicePrefixes[serviceName]) {
        return serviceName;
      }
    }

    // Check for ApiTags decorator that might indicate service
    if (controller.metadata['ApiTags']) {
      const tags = controller.metadata['ApiTags'] as string[];
      for (const tag of tags) {
        const cleanTag = tag.replace(/['"]/g, ''); // Remove quotes
        if (this.microservicePrefixes[cleanTag]) {
          return cleanTag;
        }
      }
    }

    return null;
  }

  /**
   * Strategy 2: Extract service from controller path
   * Analyzes the @Controller() path to determine which service it belongs to
   */
  private extractServiceFromControllerPath(controller: ParsedController): string | null {
    const controllerPath = controller.path;
    if (!controllerPath) {
      return null;
    }

    // Remove leading/trailing slashes and split into segments
    const pathSegments = controllerPath.replace(/^\/+|\/+$/g, '').split('/');

    // Check each segment against service names and their prefixes
    for (const serviceName of Object.keys(this.microservicePrefixes)) {
      const servicePrefix = this.microservicePrefixes[serviceName];

      // Check if controller path starts with the service prefix
      if (servicePrefix && controllerPath.startsWith(servicePrefix)) {
        return serviceName;
      }

      // Check if any path segment matches the service name
      const normalizedServiceName = serviceName.toLowerCase();
      for (const segment of pathSegments) {
        const normalizedSegment = segment.toLowerCase();
        if (normalizedSegment === normalizedServiceName) {
          return serviceName;
        }
      }
    }

    return null;
  }

  /**
   * Strategy 3: Extract service from file path patterns
   * Analyzes the file system path to determine service ownership
   */
  private extractServiceFromFilePath(controller: ParsedController): string | null {
    const filePath = controller.filePath;
    if (!filePath) {
      return null;
    }

    const normalizedPath = filePath.toLowerCase().replace(/\\/g, '/');

    // Try to match the controller file path with any of the configured service names
    for (const serviceName of Object.keys(this.microservicePrefixes)) {
      const normalizedServiceName = serviceName.toLowerCase();

      // Direct match in path
      if (normalizedPath.includes(`/${normalizedServiceName}/`) ||
          normalizedPath.includes(`/${normalizedServiceName}-`) ||
          normalizedPath.includes(`/${normalizedServiceName}_`)) {
        return serviceName;
      }

      // Match with common variations
      const serviceVariations = [
        normalizedServiceName,
        normalizedServiceName.replace(/[-_]/g, ''),
        normalizedServiceName.replace(/[-_]/g, '-'),
        normalizedServiceName.replace(/[-_]/g, '_'),
        `${normalizedServiceName}-service`,
        `${normalizedServiceName}_service`,
        `${normalizedServiceName}service`,
        `apps/${normalizedServiceName}`,
        `libs/${normalizedServiceName}`,
        `services/${normalizedServiceName}`,
        `modules/${normalizedServiceName}`
      ];

      for (const variation of serviceVariations) {
        if (normalizedPath.includes(`/${variation}/`) ||
            normalizedPath.includes(`/${variation}-`) ||
            normalizedPath.includes(`/${variation}_`)) {
          return serviceName;
        }
      }
    }

    return null;
  }

  /**
   * Strategy 4: Extract service from controller name patterns
   * Analyzes the controller class name to infer service ownership
   */
  private extractServiceFromControllerName(controller: ParsedController): string | null {
    const controllerName = controller.name.toLowerCase();

    for (const serviceName of Object.keys(this.microservicePrefixes)) {
      const normalizedServiceName = serviceName.toLowerCase();

      // Check if controller name starts with service name
      if (controllerName.startsWith(normalizedServiceName)) {
        return serviceName;
      }

      // Check if controller name contains service name
      if (controllerName.includes(normalizedServiceName)) {
        return serviceName;
      }

      // Check variations
      const serviceVariations = [
        normalizedServiceName,
        normalizedServiceName.replace(/[-_]/g, ''),
        `${normalizedServiceName}controller`,
        `${normalizedServiceName}_controller`,
        `${normalizedServiceName}-controller`
      ];

      for (const variation of serviceVariations) {
        if (controllerName.includes(variation)) {
          return serviceName;
        }
      }
    }

    return null;
  }



  /**
   * Set collection authentication
   */
  setAuth(auth: PostmanAuth): void {
    this.collection.auth = auth;
  }

  /**
   * Get the built collection
   */
  getCollection(): PostmanCollection {
    return this.collection;
  }

  /**
   * Export collection as JSON string
   */
  toJSON(): string {
    return JSON.stringify(this.collection, null, 2);
  }
}
