import { Command } from 'commander';
import inquirer from 'inquirer';
import * as fs from 'fs-extra';
import * as path from 'path';
import { config as dotenvConfig } from 'dotenv';
import { logger } from '@/utils/logger';
import { postmanClient, Workspace, Collection } from '@/postman/client';
import { PostmanError } from '@/postman/errors';

// Load environment variables from .env file in current directory
dotenvConfig({ path: path.join(process.cwd(), '.env') });

/**
 * Configuration interfaces
 */
interface AuthRule {
  extract_token_from: string;
  token_key: string;
  save_as: string;
}

interface PostmanSyncConfig {
  base_url: string;
  auth: AuthRule[];
  postman: {
    api_key: string;
    workspace_id: string;
    collection_uid: string;
    auto_push: boolean;
    confirm_on_push: boolean;
    use_env_variables: boolean;
  };
}

/**
 * Write environment variable to .env file
 */
async function writeEnvVariable(key: string, value: string): Promise<void> {
  const envPath = path.join(process.cwd(), '.env');
  let envContent = '';
  
  // Read existing .env file if it exists
  if (await fs.pathExists(envPath)) {
    envContent = await fs.readFile(envPath, 'utf-8');
  }
  
  // Check if the key already exists
  const keyRegex = new RegExp(`^${key}=.*$`, 'm');
  const newLine = `${key}=${value}`;
  
  if (keyRegex.test(envContent)) {
    // Replace existing key
    envContent = envContent.replace(keyRegex, newLine);
  } else {
    // Add new key
    if (envContent && !envContent.endsWith('\n')) {
      envContent += '\n';
    }
    envContent += newLine + '\n';
  }
  
  await fs.writeFile(envPath, envContent);
  logger.debug(`Updated .env file with ${key}`, { module: 'init' });
}

/**
 * Prompt for Postman API key
 */
async function promptForApiKey(): Promise<string> {
  const { apiKeyChoice } = await inquirer.prompt([
    {
      type: 'list',
      name: 'apiKeyChoice',
      message: 'How would you like to provide your Postman API Key?',
      choices: [
        { name: 'Enter API key now', value: 'enter' },
        { name: 'Use existing value from .env file', value: 'env' },
      ],
    },
  ]);

  if (apiKeyChoice === 'env') {
    const apiKey = process.env['POSTMAN_API_KEY'];
    if (!apiKey) {
      console.error('❌  POSTMAN_API_KEY not found in environment variables.');
      console.log('💡  Please set POSTMAN_API_KEY in your .env file or choose to enter it now.');
      process.exit(1);
    }
    return apiKey;
  }

  const { apiKey } = await inquirer.prompt([
    {
      type: 'password',
      name: 'apiKey',
      message: 'Enter your Postman API Key:',
      mask: '*',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'API key cannot be empty';
        }
        return true;
      },
    },
  ]);

  // Write to .env file
  await writeEnvVariable('POSTMAN_API_KEY', apiKey);
  
  // Set in current process for immediate use
  process.env['POSTMAN_API_KEY'] = apiKey;
  
  console.log('✅  API key saved to .env file');
  return apiKey;
}

/**
 * Prompt for workspace selection
 */
async function promptForWorkspace(): Promise<{ workspace: Workspace, isNew: boolean }> {
  console.log('📂  Fetching your Postman workspaces...');
  try {
    const workspaces = await postmanClient.listWorkspaces();
    if (workspaces.length === 0) {
      console.log('ℹ️   No workspaces found. Creating a new one.');
      return await promptCreateWorkspace();
    }
    const choices = [
      ...workspaces.map(ws => ({
        name: `${ws.name} (${ws.type}) - ${ws.id}`,
        value: ws,
      })),
      {
        name: '➕ Create a new workspace',
        value: 'create_new',
      },
    ];
    const { selectedWorkspace } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedWorkspace',
        message: 'Select a workspace to use:',
        choices,
        pageSize: 10,
      },
    ]);
    if (selectedWorkspace === 'create_new') {
      return await promptCreateWorkspace();
    }
    return { workspace: selectedWorkspace as Workspace, isNew: false };
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`Failed to fetch workspaces: ${error.getFormattedMessage()}`);
      console.error(`❌  [postman] POST /workspaces – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to list workspaces. Please check your API key or network connection.');
    } else {
      logger.error(`Unexpected error: ${(error as Error).message}`);
      console.error('❌  An unexpected error occurred while fetching workspaces.');
    }
    process.exit(1);
  }
}

/**
 * Prompt to create a new workspace
 */
async function promptCreateWorkspace(): Promise<{ workspace: Workspace, isNew: boolean }> {
  const { workspaceName, workspaceType, workspaceDescription } = await inquirer.prompt([
    {
      type: 'input',
      name: 'workspaceName',
      message: 'Enter the name for the new workspace:',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'Workspace name cannot be empty';
        }
        return true;
      },
    },
    {
      type: 'list',
      name: 'workspaceType',
      message: 'Select workspace type:',
      choices: [
        { name: 'Personal', value: 'personal' },
        { name: 'Team', value: 'team' },
      ],
      default: 'personal',
    },
    {
      type: 'input',
      name: 'workspaceDescription',
      message: 'Enter a description for the workspace (optional):',
    },
  ]);
  try {
    console.log('✨  Creating new workspace...');
    const workspace = await postmanClient.createWorkspace(
      workspaceName.trim(),
      workspaceType,
      workspaceDescription?.trim() || undefined
    );
    console.log(`✅  Created workspace: ${workspace.name}`);
    return { workspace, isNew: true };
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`❌  [postman] POST /workspaces – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to create workspace. Please try again.');
    } else {
      logger.error(`❌  [postman] POST /workspaces – : ${(error as Error).message}`);
      if (process.argv.includes('--debug')) {
        console.error((error as Error).stack);
      }
      console.error('❌  An unexpected error occurred while creating the workspace.');
    }
    process.exit(1);
  }
}

/**
 * Prompt for collection selection
 */
async function promptForCollection(workspaceId: string): Promise<Collection> {
  console.log('📄  Fetching collections in workspace...');
  
  try {
    const collections = await postmanClient.listCollections(workspaceId);
    
    if (collections.length === 0) {
      console.log('ℹ️   No collections found in this workspace. Creating a new one.');
      return await promptCreateCollection(workspaceId);
    }

    const choices = [
      ...collections.map(collection => ({
        name: `${collection.name} - ${collection.uid}`,
        value: collection,
      })),
      {
        name: '➕ Create a new collection',
        value: 'create_new',
      },
    ];

    const { selectedCollection } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedCollection',
        message: 'Select an existing collection to sync:',
        choices,
        pageSize: 10,
      },
    ]);

    if (selectedCollection === 'create_new') {
      return await promptCreateCollection(workspaceId);
    }

    return selectedCollection as Collection;
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`Failed to fetch collections: ${error.getFormattedMessage()}`);
      console.error(`❌  [postman] POST /collections?workspace=${workspaceId} – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to list collections. Please try again.');
    } else {
      logger.error(`Unexpected error: ${(error as Error).message}`);
      console.error('❌  An unexpected error occurred while fetching collections.');
    }
    process.exit(1);
  }
}

/**
 * Prompt to create a new collection
 */
async function promptCreateCollection(workspaceId: string): Promise<Collection> {
  const { collectionName, collectionDescription } = await inquirer.prompt([
    {
      type: 'input',
      name: 'collectionName',
      message: 'Enter the name for the new collection:',
      default: 'NestJS API Collection',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'Collection name cannot be empty';
        }
        return true;
      },
    },
    {
      type: 'input',
      name: 'collectionDescription',
      message: 'Enter a description for the collection (optional):',
      default: 'Auto-generated collection from NestJS controllers',
    },
  ]);

  try {
    console.log('✨  Creating new collection...');
    // Always use the required schema
    const collection = await postmanClient.createCollection(
      workspaceId,
      collectionName.trim(),
      collectionDescription?.trim() || undefined
    );
    console.log(`✅  Created collection: ${collection.name}`);
    return collection;
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`❌  [postman] POST /collections?workspace=${workspaceId} – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to create collection. Please try again.');
    } else {
      logger.error(`❌  [postman] POST /collections?workspace=${workspaceId} – : ${(error as Error).message}`);
      if (process.argv.includes('--debug')) {
        console.error((error as Error).stack);
      }
      console.error('❌  An unexpected error occurred while creating the collection.');
    }
    process.exit(1);
  }
}

/**
 * Prompt for JWT test-script rules
 */
async function promptForAuthRules(): Promise<AuthRule[]> {
  const { ruleCount } = await inquirer.prompt({
    type: 'number',
    name: 'ruleCount',
    message: 'How many auth-token extraction rules would you like to configure?',
    default: 1,
    validate: (input: number | undefined) => {
      if (input === undefined || input < 0) {
        return 'Number of rules cannot be negative';
      }
      if (input > 10) {
        return 'Maximum 10 rules allowed';
      }
      return true;
    },
  });

  const rules: AuthRule[] = [];

  for (let i = 0; i < ruleCount; i++) {
    console.log(`\n🔐  Configuring auth rule ${i + 1} of ${ruleCount}:`);
    
    const { route, responseKey, envVarName } = await inquirer.prompt([
      {
        type: 'input',
        name: 'route',
        message: 'Enter the endpoint path (e.g. /auth/login):',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Endpoint path cannot be empty';
          }
          if (!input.startsWith('/')) {
            return 'Endpoint path must start with /';
          }
          return true;
        },
      },
      {
        type: 'input',
        name: 'responseKey',
        message: 'Enter the JSON key or path to the token (e.g. access_token or data.token):',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Response key cannot be empty';
          }
          return true;
        },
      },
      {
        type: 'input',
        name: 'envVarName',
        message: 'What environment variable name should this token be saved as?',
        default: i === 0 ? 'jwt_token' : `jwt_token_${i + 1}`,
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Environment variable name cannot be empty';
          }
          if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(input)) {
            return 'Environment variable name must be valid (letters, numbers, underscores only)';
          }
          return true;
        },
      },
    ]);

    rules.push({
      extract_token_from: route.trim(),
      token_key: responseKey.trim(),
      save_as: envVarName.trim(),
    });
  }

  return rules;
}

/**
 * Prompt for auto-push and confirmation settings
 */
async function promptForSyncSettings(): Promise<{ autoPush: boolean; confirmOnPush: boolean; baseUrl: string }> {
  const { baseUrl, autoPush, confirmOnPush } = await inquirer.prompt([
    {
      type: 'input',
      name: 'baseUrl',
      message: 'Enter your API base URL:',
      default: 'http://localhost:3000',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'Base URL cannot be empty';
        }
        try {
          new URL(input);
          return true;
        } catch {
          return 'Please enter a valid URL';
        }
      },
    },
    {
      type: 'confirm',
      name: 'autoPush',
      message: 'Would you like to automatically push changes on watch mode?',
      default: false,
    },
    {
      type: 'confirm',
      name: 'confirmOnPush',
      message: 'Require confirmation before each push?',
      default: true,
    },
  ]);

  return {
    baseUrl: baseUrl.trim(),
    autoPush,
    confirmOnPush,
  };
}

/**
 * Write configuration file
 */
async function writeConfigFile(config: PostmanSyncConfig): Promise<void> {
  const configPath = path.join(process.cwd(), 'postman.config.json');
  
  try {
    await fs.writeJson(configPath, config, { spaces: 2 });
    logger.info(`Configuration saved to ${configPath}`, { module: 'init' });
  } catch (error) {
    logger.error('Failed to write configuration file', error as Error, { module: 'init' });
    console.error('❌  Failed to save configuration file.');
    process.exit(1);
  }
}

/**
 * Main init command implementation
 */
export const initCommand = new Command('init')
  .description('Initialize the configuration for nest-postman-sync')
  .action(async () => {
    console.log('🔧  Initializing nest-postman-sync...\n');
    logger.info('Starting init command', { module: 'init' });

    try {
      // Step 1: Get API key
      await promptForApiKey();
      console.log();

      // Step 2: Select or create workspace
      const { workspace, isNew } = await promptForWorkspace();
      console.log(`📁  Selected workspace: ${workspace.name}\n`);

      // Step 3: Select or create collection
      let collection: Collection;
      if (isNew) {
        // Always create a new collection for a new workspace
        collection = await promptCreateCollection(workspace.id);
      } else {
        // Prompt for existing or new collection in the selected workspace
        collection = await promptForCollection(workspace.id);
      }
      console.log(`📄  Selected collection: ${collection.name}\n`);

      // Step 4: Configure auth rules
      console.log('🔐  Configuring authentication rules...');
      const authRules = await promptForAuthRules();
      console.log();

      // Step 5: Configure sync settings
      console.log('⚙️   Configuring sync settings...');
      const syncSettings = await promptForSyncSettings();
      console.log();

      // Step 6: Build and write configuration
      const config: PostmanSyncConfig = {
        base_url: syncSettings.baseUrl,
        auth: authRules,
        postman: {
          api_key: 'env:POSTMAN_API_KEY',
          workspace_id: workspace.id,
          collection_uid: collection.uid,
          auto_push: syncSettings.autoPush,
          confirm_on_push: syncSettings.confirmOnPush,
          use_env_variables: true,
        },
      };

      console.log('💾  Saving configuration...');
      await writeConfigFile(config);

      // Success output
      console.log('\n✅  Saved configuration to postman.config.json');
      console.log(`✅  Workspace: ${workspace.name} (${workspace.id})`);
      console.log(`✅  Collection: ${collection.name} (${collection.uid})`);
      console.log('Next: run `nest-postman-sync generate`');
      logger.info('Init command completed successfully', { module: 'init' });
    } catch (error) {
      logger.error('Init command failed', error as Error, { module: 'init' });
      if (error instanceof PostmanError) {
        const meta = error.meta || {};
        const method = meta.method || '';
        const url = meta.url || '';
        const status = meta.status || '';
        const message = error.message || '';
        console.error(`❌  [postman] ${method} ${url} – ${status} : ${message}`);
        if (process.argv.includes('--debug')) {
          console.error(error.stack);
        }
      } else {
        console.error(`❌  An unexpected error occurred: ${(error as Error).message}`);
        if (process.argv.includes('--debug')) {
          console.error((error as Error).stack);
        }
      }
      process.exit(1);
    }
  });
