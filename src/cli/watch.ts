import { Command } from 'commander';
import * as chokidar from 'chokidar';
import * as path from 'path';
import { logger } from '@/utils/logger';
import { loadConfig } from '@/config/config-loader';
import { generateCommand } from './generate';
import { syncCommand } from './sync';

export const watchCommand = new Command('watch')
  .description('Watch for file changes and automatically sync to Postman')
  .option('--debounce <ms>', 'Debounce delay in milliseconds', '1000')
  .option('--generate-only', 'Only generate collection, do not sync to Postman', false)
  .option('--output <file>', 'Output file path', 'postman-collection.json')
  .action(async (options) => {
    try {
      logger.info('Starting watch mode', { module: 'watch' });

      // Load configuration
      const config = await loadConfig();
      logger.debug('Configuration loaded', { module: 'watch' });

      const debounceMs = parseInt(options.debounce, 10);
      let timeoutId: NodeJS.Timeout | null = null;
      let isProcessing = false;

      console.log('👀 Watching for file changes...');
      console.log(`📁 Source directory: ${config.nestjs.srcDir}`);
      console.log(`⏱️  Debounce delay: ${debounceMs}ms`);
      console.log('Press Ctrl+C to stop watching\n');

      // Set up file watcher
      const watchPaths = config.sync.watchPaths || ['src/**/*.ts'];
      const watcher = chokidar.watch(watchPaths, {
        ignored: [
          '**/node_modules/**',
          '**/.git/**',
          '**/*.spec.ts',
          '**/*.test.ts',
          ...(config.nestjs.excludePatterns || []),
        ],
        persistent: true,
        ignoreInitial: true,
      });

      // Debounced regeneration function
      const debouncedRegenerate = () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        timeoutId = setTimeout(async () => {
          if (isProcessing) {
            logger.debug('Already processing, skipping regeneration', { module: 'watch' });
            return;
          }

          isProcessing = true;

          try {
            console.log('\n🔄 Changes detected, regenerating collection...');

            // Run generate command
            await runGenerate(options.output);

            // Run sync command if not generate-only
            if (!options.generateOnly) {
              await runSync(options.output);
            }

            console.log('✅ Update completed\n');
            console.log('👀 Watching for more changes...');

          } catch (error) {
            console.error('❌ Update failed:', (error as Error).message);
            logger.error('Watch regeneration failed', error as Error, { module: 'watch' });
          } finally {
            isProcessing = false;
          }
        }, debounceMs);
      };

      // Set up event handlers
      watcher
        .on('add', (filePath) => {
          logger.debug(`File added: ${filePath}`, { module: 'watch' });
          console.log(`➕ Added: ${path.relative(process.cwd(), filePath)}`);
          debouncedRegenerate();
        })
        .on('change', (filePath) => {
          logger.debug(`File changed: ${filePath}`, { module: 'watch' });
          console.log(`📝 Changed: ${path.relative(process.cwd(), filePath)}`);
          debouncedRegenerate();
        })
        .on('unlink', (filePath) => {
          logger.debug(`File removed: ${filePath}`, { module: 'watch' });
          console.log(`🗑️  Removed: ${path.relative(process.cwd(), filePath)}`);
          debouncedRegenerate();
        })
        .on('error', (error) => {
          logger.error('Watcher error', error, { module: 'watch' });
          console.error('❌ Watcher error:', error.message);
        });

      // Handle graceful shutdown
      process.on('SIGINT', () => {
        console.log('\n🛑 Stopping watch mode...');
        watcher.close();
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        process.exit(0);
      });

      // Keep the process alive
      await new Promise(() => {}); // Never resolves

    } catch (error) {
      logger.error('Watch command failed', error as Error, { module: 'watch' });
      console.error('❌ Watch failed:', (error as Error).message);
      process.exit(1);
    }
  });

/**
 * Run the generate command programmatically
 */
async function runGenerate(outputFile: string): Promise<void> {
  const { spawn } = await import('child_process');

  return new Promise((resolve, reject) => {
    const child = spawn('node', [
      'dist/index.js',
      'generate',
      '--output', outputFile,
      '--format'
    ], {
      stdio: 'inherit',
      cwd: process.cwd(),
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Generate command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Run the sync command programmatically
 */
async function runSync(inputFile: string): Promise<void> {
  const { spawn } = await import('child_process');

  return new Promise((resolve, reject) => {
    const child = spawn('node', [
      'dist/index.js',
      'sync',
      '--input', inputFile
    ], {
      stdio: 'inherit',
      cwd: process.cwd(),
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Sync command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}
