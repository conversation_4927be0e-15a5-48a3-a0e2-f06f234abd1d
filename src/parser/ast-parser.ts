import { Project, ClassDeclaration, MethodDeclaration, Decorator, ParameterDeclaration, SyntaxKind } from 'ts-morph';
import * as path from 'path';
import { logger } from '@/utils/logger';
import { dtoGenerator, DTOExample } from './dto-generator';

export interface ParsedController {
  name: string;
  path: string;
  methods: ParsedMethod[];
  metadata: Record<string, unknown>;
  guards: string[];
  filePath: string;
}

export interface ParsedMethod {
  name: string;
  httpMethod: string;
  path: string;
  parameters: ParsedParameter[];
  responses: ParsedResponse[];
  description?: string | undefined;
  deprecated?: boolean;
  guards: string[];
  summary?: string | undefined;
}

export interface ParsedParameter {
  name: string;
  type: string;
  location: 'query' | 'body' | 'path' | 'header';
  required: boolean;
  description?: string;
  example?: any;
  schema?: DTOExample;
}

export interface ParsedResponse {
  statusCode: number;
  description?: string;
  schema?: Record<string, unknown>;
}

export interface ParseOptions {
  tsConfigPath?: string;
  includePrivate?: boolean;
  includeDeprecated?: boolean;
}

export class ASTParser {
  private project: Project;
  private dtoCache = new Map<string, ClassDeclaration>();

  constructor(options: ParseOptions = {}) {
    this.project = new Project({
      tsConfigFilePath: options.tsConfigPath || 'tsconfig.json',
      skipAddingFilesFromTsConfig: true,
      useInMemoryFileSystem: false,
    });
  }

  /**
   * Parse NestJS controller files and extract API information
   */
  async parseControllers(filePaths: string[]): Promise<ParsedController[]> {
    logger.debug(`Parsing ${filePaths.length} controller files`, { module: 'parser' });

    try {
      // Add source files to project
      const sourceFiles = this.project.addSourceFilesAtPaths(filePaths);

      const controllers: ParsedController[] = [];

      for (const sourceFile of sourceFiles) {
        const controller = await this.parseController(sourceFile.getFilePath());
        if (controller) {
          controllers.push(controller);
        }
      }

      logger.debug(`Successfully parsed ${controllers.length} controllers`, { module: 'parser' });
      return controllers;
    } catch (error) {
      logger.error('Failed to parse controllers', error as Error, { module: 'parser' });
      throw error;
    }
  }

  /**
   * Parse a single controller file
   */
  async parseController(filePath: string): Promise<ParsedController | null> {
    logger.debug(`Parsing controller file: ${filePath}`, { module: 'parser' });

    try {
      const sourceFile = this.project.getSourceFile(filePath) || this.project.addSourceFileAtPath(filePath);

      // Find controller class
      const controllerClass = this.findControllerClass(sourceFile.getClasses());
      if (!controllerClass) {
        logger.warn(`No controller class found in: ${filePath}`, { module: 'parser' });
        return null;
      }

      const controllerDecorator = this.findControllerDecorator(controllerClass);
      if (!controllerDecorator) {
        logger.warn(`No @Controller decorator found in: ${filePath}`, { module: 'parser' });
        return null;
      }

      const controllerPath = this.extractControllerPath(controllerDecorator);
      const controllerGuards = this.extractGuards(controllerClass.getDecorators());

      // Parse methods
      const methods = await this.parseControllerMethods(controllerClass);

      const controller: ParsedController = {
        name: controllerClass.getName() || 'UnknownController',
        path: controllerPath,
        methods,
        metadata: this.extractClassMetadata(controllerClass),
        guards: controllerGuards,
        filePath: path.normalize(filePath),
      };

      logger.debug(`Parsed controller: ${controller.name} with ${methods.length} methods`, { module: 'parser' });
      return controller;
    } catch (error) {
      logger.error(`Failed to parse controller file: ${filePath}`, error as Error, { module: 'parser' });
      return null;
    }
  }

  /**
   * Find controller class in the source file
   */
  private findControllerClass(classes: ClassDeclaration[]): ClassDeclaration | null {
    for (const cls of classes) {
      const decorators = cls.getDecorators();
      if (decorators.some(d => d.getName() === 'Controller')) {
        return cls;
      }
    }
    return null;
  }

  /**
   * Find @Controller decorator
   */
  private findControllerDecorator(controllerClass: ClassDeclaration): Decorator | null {
    return controllerClass.getDecorators().find(d => d.getName() === 'Controller') || null;
  }

  /**
   * Extract controller path from @Controller decorator
   */
  private extractControllerPath(decorator: Decorator): string {
    const args = decorator.getArguments();
    if (args.length > 0) {
      const pathArg = args[0];
      if (pathArg && pathArg.getKind() === SyntaxKind.StringLiteral) {
        return pathArg.getText().slice(1, -1); // Remove quotes
      }
    }
    return '';
  }

  /**
   * Extract guards from decorators
   */
  private extractGuards(decorators: Decorator[]): string[] {
    const guards: string[] = [];

    for (const decorator of decorators) {
      const name = decorator.getName();
      if (name === 'UseGuards') {
        const args = decorator.getArguments();
        for (const arg of args) {
          const guardName = arg.getText();
          guards.push(guardName);
        }
      }
    }

    return guards;
  }

  /**
   * Extract class metadata
   */
  private extractClassMetadata(controllerClass: ClassDeclaration): Record<string, unknown> {
    const metadata: Record<string, unknown> = {};

    // Extract JSDoc comments
    const jsDocs = controllerClass.getJsDocs();
    if (jsDocs.length > 0) {
      metadata['description'] = jsDocs[0]?.getDescription() || '';
    }

    // Extract other decorators
    const decorators = controllerClass.getDecorators();
    for (const decorator of decorators) {
      const name = decorator.getName();
      if (name && name !== 'Controller' && name !== 'UseGuards') {
        metadata[name] = decorator.getArguments().map(arg => arg.getText());
      }
    }

    return metadata;
  }

  /**
   * Parse controller methods
   */
  private async parseControllerMethods(controllerClass: ClassDeclaration): Promise<ParsedMethod[]> {
    const methods: ParsedMethod[] = [];

    for (const method of controllerClass.getMethods()) {
      const parsedMethod = await this.parseMethod(method);
      if (parsedMethod) {
        methods.push(parsedMethod);
      }
    }

    return methods;
  }

  /**
   * Parse a single method
   */
  private async parseMethod(method: MethodDeclaration): Promise<ParsedMethod | null> {
    const decorators = method.getDecorators();
    const httpDecorator = this.findHttpDecorator(decorators);

    if (!httpDecorator) {
      return null; // Not an HTTP method
    }

    const httpMethod = httpDecorator.getName()?.toUpperCase() || 'GET';
    const methodPath = this.extractMethodPath(httpDecorator);
    const methodGuards = this.extractGuards(decorators);
    const parameters = await this.parseMethodParameters(method);

    // Extract JSDoc for description
    const jsDocs = method.getJsDocs();
    const description = jsDocs.length > 0 ? jsDocs[0]?.getDescription() || undefined : undefined;

    // Check for deprecated decorator
    const isDeprecated = decorators.some(d => d.getName() === 'Deprecated');

    return {
      name: method.getName(),
      httpMethod,
      path: methodPath,
      parameters,
      responses: this.extractResponses(decorators),
      description: description || undefined,
      deprecated: isDeprecated,
      guards: methodGuards,
      summary: description || undefined,
    };
  }

  /**
   * Find HTTP method decorator
   */
  private findHttpDecorator(decorators: Decorator[]): Decorator | null {
    const httpMethods = ['Get', 'Post', 'Put', 'Delete', 'Patch', 'Options', 'Head'];

    for (const decorator of decorators) {
      const name = decorator.getName();
      if (name && httpMethods.includes(name)) {
        return decorator;
      }
    }

    return null;
  }

  /**
   * Extract method path from HTTP decorator
   */
  private extractMethodPath(decorator: Decorator): string {
    const args = decorator.getArguments();
    if (args.length > 0) {
      const pathArg = args[0];
      if (pathArg && pathArg.getKind() === SyntaxKind.StringLiteral) {
        return pathArg.getText().slice(1, -1); // Remove quotes
      }
    }
    return '';
  }

  /**
   * Extract responses from decorators
   */
  private extractResponses(decorators: Decorator[]): ParsedResponse[] {
    const responses: ParsedResponse[] = [];

    for (const decorator of decorators) {
      const name = decorator.getName();
      if (name === 'ApiResponse' || name === 'HttpCode') {
        const args = decorator.getArguments();
        if (args.length > 0) {
          const statusCode = parseInt(args[0]?.getText() || '200', 10);
          responses.push({
            statusCode,
            description: `${statusCode} response`,
          });
        }
      }
    }

    // Default response if none specified
    if (responses.length === 0) {
      responses.push({
        statusCode: 200,
        description: 'Success',
      });
    }

    return responses;
  }

  /**
   * Parse method parameters
   */
  private async parseMethodParameters(method: MethodDeclaration): Promise<ParsedParameter[]> {
    const parameters: ParsedParameter[] = [];

    for (const param of method.getParameters()) {
      const parsedParam = await this.parseParameter(param);
      if (parsedParam) {
        parameters.push(parsedParam);
      }
    }

    return parameters;
  }

  /**
   * Parse a single parameter
   */
  private async parseParameter(param: ParameterDeclaration): Promise<ParsedParameter | null> {
    const decorators = param.getDecorators();
    const paramDecorator = this.findParameterDecorator(decorators);

    if (!paramDecorator) {
      return null; // Not a recognized parameter decorator
    }

    const decoratorName = paramDecorator.getName();
    const location = this.getParameterLocation(decoratorName || '');
    const paramName = this.extractParameterName(paramDecorator, param.getName());
    const typeText = param.getTypeNode()?.getText() || 'any';
    const isOptional = param.hasQuestionToken();

    let example: any = undefined;
    let schema: any = undefined;

    // For body parameters, try to generate DTO example
    if (location === 'body' && typeText !== 'any') {
      const dtoClass = this.findDTOClass(typeText);
      if (dtoClass) {
        schema = dtoGenerator.generateExample(dtoClass);
        example = schema;
      }
    }

    return {
      name: paramName,
      type: typeText,
      location,
      required: !isOptional,
      example,
      schema,
    };
  }

  /**
   * Find parameter decorator
   */
  private findParameterDecorator(decorators: Decorator[]): Decorator | null {
    const paramDecorators = ['Body', 'Query', 'Param', 'Headers', 'Header'];

    for (const decorator of decorators) {
      const name = decorator.getName();
      if (name && paramDecorators.includes(name)) {
        return decorator;
      }
    }

    return null;
  }

  /**
   * Get parameter location from decorator name
   */
  private getParameterLocation(decoratorName: string): 'query' | 'body' | 'path' | 'header' {
    switch (decoratorName) {
      case 'Body':
        return 'body';
      case 'Query':
        return 'query';
      case 'Param':
        return 'path';
      case 'Headers':
      case 'Header':
        return 'header';
      default:
        return 'query';
    }
  }

  /**
   * Extract parameter name from decorator or use default
   */
  private extractParameterName(decorator: Decorator, defaultName: string): string {
    const args = decorator.getArguments();
    if (args.length > 0) {
      const nameArg = args[0];
      if (nameArg && nameArg.getKind() === SyntaxKind.StringLiteral) {
        return nameArg.getText().slice(1, -1); // Remove quotes
      }
    }
    return defaultName;
  }

  /**
   * Find DTO class by name
   */
  private findDTOClass(typeName: string): ClassDeclaration | null {
    // Check cache first
    if (this.dtoCache.has(typeName)) {
      return this.dtoCache.get(typeName)!;
    }

    // Search in all source files
    for (const sourceFile of this.project.getSourceFiles()) {
      const classes = sourceFile.getClasses();
      for (const cls of classes) {
        if (cls.getName() === typeName) {
          this.dtoCache.set(typeName, cls);
          return cls;
        }
      }
    }

    return null;
  }

  /**
   * Clear internal caches
   */
  clearCache(): void {
    this.dtoCache.clear();
    dtoGenerator.clearCache();
  }
}
