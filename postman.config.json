{"postman": {"apiKey": "your-postman-api-key-here", "collectionId": "your-collection-id-here", "workspaceId": "your-workspace-id-here", "baseUrl": "https://api.postman.com"}, "nestjs": {"projectRoot": ".", "srcDir": "src", "controllersPattern": ["**/*.controller.ts"], "excludePatterns": ["**/*.spec.ts", "**/*.test.ts"]}, "sync": {"outputFile": "postman-collection.json", "watchMode": false, "watchPaths": ["src/**/*.ts"], "debounceMs": 1000}, "options": {"generateDocs": true, "includePrivate": false, "includeDeprecated": false, "overwriteExisting": true}}