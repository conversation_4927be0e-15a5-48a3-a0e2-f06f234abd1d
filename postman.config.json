{"postman": {"apiKey": "${POSTMAN_API_KEY}", "collectionId": "${POSTMAN_COLLECTION_ID}", "workspaceId": "${POSTMAN_WORKSPACE_ID}", "baseUrl": "https://api.postman.com"}, "nestjs": {"projectRoot": ".", "srcDir": "src", "controllersPattern": ["**/*.controller.ts"], "excludePatterns": ["**/*.spec.ts", "**/*.test.ts"]}, "sync": {"outputFile": "postman-collection.json", "watchMode": false, "watchPaths": ["src/**/*.ts"], "debounceMs": 1000}, "options": {"generateDocs": true, "includePrivate": false, "includeDeprecated": false, "overwriteExisting": true}}