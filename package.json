{"name": "nest-postman-sync", "version": "1.0.0", "description": "A production-grade CLI tool for syncing NestJS projects with Postman collections", "main": "dist/index.js", "bin": {"nest-postman-sync": "./dist/index.js"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "ts-node -r tsconfig-paths/register src/index.ts", "start": "node dist/index.js", "test": "jest --watch", "test:ci": "jest --ci --coverage", "coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "prepare": "npm run build", "prepublishOnly": "npm run test:ci && npm run lint && npm run build", "release": "np", "postinstall": "node -e \"console.log('\\n✅ nest-postman-sync installed successfully!\\n💡 Run: nest-postman-sync init\\n')\"", "verify": "node scripts/verify-installation.js"}, "keywords": ["<PERSON><PERSON><PERSON>", "postman", "cli", "sync", "api", "documentation", "typescript"], "author": "<PERSON><PERSON><PERSON> william", "license": "MIT", "files": ["dist/**/*", "README.md", "LICENSE"], "engines": {"node": ">=16.0.0", "npm": ">=7.0.0", "yarn": ">=1.22.0"}, "dependencies": {"@types/inquirer": "^9.0.8", "axios": "^1.6.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "commander": "^11.1.0", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "glob": "^10.3.10", "inquirer": "^12.6.3", "joi": "^17.11.0", "module-alias": "^2.2.3", "ts-morph": "^20.0.0"}, "devDependencies": {"@types/fs-extra": "^11.0.3", "@types/jest": "^29.5.5", "@types/module-alias": "^2.0.4", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.28.1", "jest": "^29.7.0", "np": "^8.0.4", "prettier": "^3.0.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}}