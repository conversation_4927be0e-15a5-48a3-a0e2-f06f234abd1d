{"postman": {"apiKey": "${POSTMAN_API_KEY}", "collectionId": "your-collection-id", "workspaceId": "your-workspace-id", "baseUrl": "https://api.postman.com"}, "nestjs": {"projectRoot": ".", "autoDiscovery": true, "srcDirs": ["src"], "controllersPattern": ["**/*.controller.ts"], "excludePatterns": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/.git/**", "**/*.spec.ts", "**/*.test.ts"], "globalPrefix": "", "autoDetectGlobalPrefix": false, "isMicroservice": true, "microservicePrefixes": {"auth": "/auth", "users": "/api/v1/users", "orders": "/api/v1/orders", "payments": "/api/v1/payments", "notifications": "/notifications", "admin": "/admin"}}, "sync": {"outputFile": "postman-collection.json", "watchMode": false, "watchPaths": ["src/**/*.ts"], "debounceMs": 1000}, "options": {"generateDocs": true, "includePrivate": false, "includeDeprecated": false, "overwriteExisting": true}}